"""
CLDNN (Convolutional, LSTM, Deep Neural Network) PyTorch实现

原始论文: CLDNN模型用于自动调制识别
原始实现: TensorFlow/Keras
本实现: PyTorch

模型结构:
1. 三个2D卷积层，每层包含Conv2D + Dropout
2. 跳跃连接：将第一个和第三个卷积层的输出连接
3. LSTM层处理时序特征
4. 全连接层进行分类

主要特点:
- 使用2D卷积处理I/Q信号
- 跳跃连接增强特征传播
- LSTM捕获时序依赖
- 高dropout正则化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class CLDNN(nn.Module):
    """
    CLDNN模型的PyTorch实现
    
    Args:
        in_channels (int): 输入通道数，默认为2 (I/Q)
        num_classes (int): 分类类别数
        sequence_length (int): 输入序列长度
        dropout_rate (float): Dropout比率，默认为0.5
        conv_channels (int): 卷积层通道数，默认为50
        lstm_units (int): LSTM隐藏单元数，默认为50
        fc_units (int): 全连接层单元数，默认为256
        conv_kernel_size (int): 卷积核大小，默认为8
    """
    
    def __init__(self, 
                 in_channels=2,
                 num_classes=11,
                 sequence_length=128,
                 dropout_rate=0.5,
                 conv_channels=50,
                 lstm_units=50,
                 fc_units=256,
                 conv_kernel_size=8):
        super(CLDNN, self).__init__()
        
        self.in_channels = in_channels
        self.num_classes = num_classes
        self.sequence_length = sequence_length
        self.dropout_rate = dropout_rate
        self.conv_channels = conv_channels
        self.lstm_units = lstm_units
        self.fc_units = fc_units
        self.conv_kernel_size = conv_kernel_size
        
        # 计算padding以保持序列长度
        self.padding = (conv_kernel_size - 1) // 2
        
        # 第一个卷积层
        self.conv1 = nn.Conv2d(
            in_channels=1,  # 输入形状 [B, 1, 2, L]
            out_channels=conv_channels,
            kernel_size=(1, conv_kernel_size),
            padding=(0, self.padding)
        )
        self.dropout1 = nn.Dropout2d(dropout_rate)
        
        # 第二个卷积层
        self.conv2 = nn.Conv2d(
            in_channels=conv_channels,
            out_channels=conv_channels,
            kernel_size=(1, conv_kernel_size),
            padding=(0, self.padding)
        )
        self.dropout2 = nn.Dropout2d(dropout_rate)
        
        # 第三个卷积层
        self.conv3 = nn.Conv2d(
            in_channels=conv_channels,
            out_channels=conv_channels,
            kernel_size=(1, conv_kernel_size),
            padding=(0, self.padding)
        )
        self.dropout3 = nn.Dropout2d(dropout_rate)
        
        # 计算LSTM输入维度
        self._calculate_lstm_input_dim()
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=self.lstm_input_size,
            hidden_size=lstm_units,
            batch_first=True
        )
        
        # 全连接层
        self.fc1 = nn.Linear(lstm_units, fc_units)
        self.dropout_fc = nn.Dropout(dropout_rate)
        self.fc2 = nn.Linear(fc_units, num_classes)
        
        # 初始化权重
        self._initialize_weights()
    
    def _calculate_lstm_input_dim(self):
        """计算LSTM输入维度"""
        # 创建一个dummy输入来计算维度
        dummy_input = torch.zeros(1, 1, self.in_channels, self.sequence_length)
        
        # 第一个卷积层
        x1 = F.relu(self.conv1(dummy_input))
        
        # 第二个卷积层
        x2 = F.relu(self.conv2(x1))
        
        # 第三个卷积层
        x3 = F.relu(self.conv3(x2))
        
        # 确保x1和x3的尺寸匹配（处理不同序列长度导致的尺寸不匹配）
        if x1.shape[-1] != x3.shape[-1]:
            # 取较小的width作为目标尺寸
            target_width = min(x1.shape[-1], x3.shape[-1])
            x1 = x1[:, :, :, :target_width]
            x3 = x3[:, :, :, :target_width]

        # 连接第一个和第三个卷积层
        x_concat = torch.cat([x1, x3], dim=1)  # [B, 2*conv_channels, H, W]
        
        # 计算LSTM输入维度
        batch_size, channels, height, width = x_concat.shape
        self.lstm_input_size = channels * height
        self.feature_width = width
        
    def _initialize_weights(self):
        """初始化模型权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                # 使用Glorot uniform初始化（对应原始的glorot_uniform）
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                # 全连接层使用He normal初始化（对应原始的he_normal）
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LSTM):
                # LSTM权重初始化
                for name, param in m.named_parameters():
                    if 'weight_ih' in name:
                        nn.init.xavier_uniform_(param.data)
                    elif 'weight_hh' in name:
                        nn.init.orthogonal_(param.data)
                    elif 'bias' in name:
                        nn.init.constant_(param.data, 0)
                        # 设置forget gate bias为1（LSTM的常见做法）
                        n = param.size(0)
                        param.data[n//4:n//2].fill_(1.)
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x: 输入张量，形状为 [batch_size, 2, sequence_length]
            
        Returns:
            output: 分类输出，形状为 [batch_size, num_classes]
        """
        batch_size = x.size(0)
        
        # 调整输入形状: [B, 2, L] -> [B, 1, 2, L]
        if x.dim() == 3:
            x = x.unsqueeze(1)
        
        # 第一个卷积层
        x1 = F.relu(self.conv1(x))
        x1 = self.dropout1(x1)
        
        # 第二个卷积层
        x2 = F.relu(self.conv2(x1))
        x2 = self.dropout2(x2)
        
        # 第三个卷积层
        x3 = F.relu(self.conv3(x2))
        x3 = self.dropout3(x3)
        
        # 确保x1和x3的尺寸匹配（处理不同序列长度导致的尺寸不匹配）
        if x1.shape[-1] != x3.shape[-1]:
            # 取较小的width作为目标尺寸
            target_width = min(x1.shape[-1], x3.shape[-1])
            x1 = x1[:, :, :, :target_width]
            x3 = x3[:, :, :, :target_width]

        # 跳跃连接：连接第一个和第三个卷积层
        x_concat = torch.cat([x1, x3], dim=1)  # [B, 2*conv_channels, H, W]
        
        # 重塑为LSTM输入格式: [B, W, 2*conv_channels*H]
        batch_size, channels, height, width = x_concat.shape
        x_reshaped = x_concat.permute(0, 3, 1, 2)  # [B, W, channels, H]
        x_reshaped = x_reshaped.contiguous().view(batch_size, width, channels * height)
        
        # LSTM层
        lstm_out, (h_n, c_n) = self.lstm(x_reshaped)  # [B, W, lstm_units]
        
        # 取最后一个时间步的输出
        lstm_final = lstm_out[:, -1, :]  # [B, lstm_units]
        
        # 全连接层
        fc1_out = F.relu(self.fc1(lstm_final))
        fc1_out = self.dropout_fc(fc1_out)
        
        # 输出层
        output = self.fc2(fc1_out)
        
        return output
    
    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'model_name': 'CLDNN',
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'input_shape': f'[batch_size, {self.in_channels}, {self.sequence_length}]',
            'output_shape': f'[batch_size, {self.num_classes}]',
            'conv_channels': self.conv_channels,
            'lstm_units': self.lstm_units,
            'fc_units': self.fc_units,
            'dropout_rate': self.dropout_rate,
            'conv_kernel_size': self.conv_kernel_size
        }


def create_cldnn_for_dataset(dataset_type, num_classes, sequence_length):
    """
    为特定数据集创建CLDNN模型
    
    Args:
        dataset_type (str): 数据集类型
        num_classes (int): 类别数
        sequence_length (int): 序列长度
        
    Returns:
        CLDNN: 配置好的CLDNN模型
    """
    # 根据数据集调整参数
    if dataset_type == 'rml':
        # RML数据集参数
        config = {
            'conv_channels': 50,
            'lstm_units': 50,
            'fc_units': 256,
            'dropout_rate': 0.5,
            'conv_kernel_size': 8
        }
    elif dataset_type == 'hisar':
        # HisarMod数据集参数
        config = {
            'conv_channels': 64,
            'lstm_units': 64,
            'fc_units': 512,
            'dropout_rate': 0.4,
            'conv_kernel_size': 8
        }
    elif dataset_type.startswith('torchsig'):
        # TorchSig数据集参数
        config = {
            'conv_channels': 80,
            'lstm_units': 80,
            'fc_units': 512,
            'dropout_rate': 0.3,
            'conv_kernel_size': 8
        }
    else:
        # 默认参数
        config = {
            'conv_channels': 50,
            'lstm_units': 50,
            'fc_units': 256,
            'dropout_rate': 0.5,
            'conv_kernel_size': 8
        }
    
    return CLDNN(
        in_channels=2,
        num_classes=num_classes,
        sequence_length=sequence_length,
        **config
    )


if __name__ == '__main__':
    # 测试模型
    model = CLDNN(num_classes=11, sequence_length=128)
    
    # 打印模型信息
    info = model.get_model_info()
    print("CLDNN模型信息:")
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # 测试前向传播
    batch_size = 4
    test_input = torch.randn(batch_size, 2, 128)
    
    with torch.no_grad():
        output = model(test_input)
        print(f"\n测试输入形状: {test_input.shape}")
        print(f"测试输出形状: {output.shape}")
        print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
