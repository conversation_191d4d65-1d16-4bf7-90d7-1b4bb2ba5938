{"test_configuration": {"train_sample_ratio": 0.2, "train_sample_percentage": "20.0%", "val_sample_ratio": 0, "val_sample_percentage": "0.0%", "test_sample_ratio": 0.2, "test_sample_percentage": "20.0%", "original_train_ratio": 0.7, "original_val_ratio": 0.15, "original_test_ratio": 0.15}, "overall_metrics": {"accuracy": 64.68499646322329, "macro_f1": 64.72748283570135, "kappa": 0.6314955759637007}, "model_complexity": {"macs": "1.123G", "parameters": "949.976K", "macs_raw": 1122598784.0, "params_raw": 949976.0}, "inference_performance": {"avg_inference_time_ms": 0.16278795294839432, "std_inference_time_ms": 0.02828243527649032, "min_inference_time_ms": 0.13405457139015198, "max_inference_time_ms": 0.7442368401421441}, "dataset_info": {"total_samples": 434011, "dataset_type": "rml201801a", "input_shape": [2, 1024], "num_classes": 24, "snr_range": [-20.0, 30.0]}, "test_info": {"model_path": "./saved_models/cldnn/rml201801a_20250620_105807/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-07-03 16:14:40", "script_name": "test_rml2018_custom_ratio.py"}}